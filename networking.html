<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشبكات - MCT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #28909A;
            --secondary-color: #1c5c62;
            --accent-color-1: #F7A600;
            --accent-color-2: #008B80;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
            line-height: 1.6;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
        }

        /* نقاط متحركة */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgb(0, 151, 139);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-30px) rotate(180deg); opacity: 1; }
        }

        /* Header */
        .header {
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 5%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
        }

        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #008B80, #1c5c62);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 139, 128, 0.4);
        }

        /* Main Content */
        .main-content {
            margin-top: 100px;
            padding: 50px 5%;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .service-hero {
            text-align: center;
            margin-bottom: 80px;
        }

        .service-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 50px;
            color: white;
            box-shadow: 0 20px 40px rgba(0, 139, 128, 0.3);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .service-title {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-subtitle {
            font-size: 1.3rem;
            color: #b0b0b0;
            max-width: 800px;
            margin: 0 auto;
        }

        .content-section {
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 2.2rem;
            color: #008B80;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1));
            transition: left 0.5s ease;
            z-index: -1;
        }

        .feature-card:hover::before {
            left: 0;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 139, 128, 0.3);
            box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2);
        }

        .feature-card h3 {
            font-size: 1.4rem;
            color: #F7A600;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #c0c0c0;
            line-height: 1.7;
        }

        .services-list {
            list-style: none;
            padding: 0;
        }

        .services-list li {
            background: rgba(255, 255, 255, 0.05);
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #008B80;
            transition: all 0.3s ease;
        }

        .services-list li:hover {
            background: rgba(0, 139, 128, 0.1);
            transform: translateX(10px);
        }

        .contact-cta {
            text-align: center;
            margin-top: 60px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cta-button {
            padding: 15px 40px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 139, 128, 0.5);
        }

        @media (max-width: 768px) {
            .service-title {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 30px 5%;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">MCT</div>
                <div class="company-name">MCT</div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button id="lang-toggle" style="
                    background: linear-gradient(135deg, #008B80, #1c5c62);
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 0.9rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero">
            <div class="service-icon">🌐</div>
            <h1 class="service-title">الشبكات</h1>
            <p class="service-subtitle">
                نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع أنظمتكم. 
                من التصميم والتنفيذ إلى الصيانة والدعم، نحن شريككم الموثوق في عالم الشبكات.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section">
            <h2 class="section-title">خدماتنا في الشبكات</h2>
            <ul class="services-list">
                <li><strong>تصميم الشبكات:</strong> تصميم شبكات مخصصة تتناسب مع احتياجاتكم وحجم أعمالكم</li>
                <li><strong>تنفيذ وتركيب:</strong> تنفيذ احترافي للشبكات باستخدام أحدث المعدات والتقنيات</li>
                <li><strong>الشبكات اللاسلكية:</strong> حلول Wi-Fi متقدمة وآمنة لجميع البيئات</li>
                <li><strong>شبكات الخوادم:</strong> إعداد وإدارة شبكات الخوادم عالية الأداء</li>
                <li><strong>الصيانة الدورية:</strong> خدمات صيانة شاملة لضمان استمرارية الشبكة</li>
                <li><strong>مراقبة الشبكة:</strong> مراقبة مستمرة للأداء واكتشاف المشاكل مبكراً</li>
                <li><strong>ترقية الشبكات:</strong> تحديث وترقية الشبكات الموجودة لتحسين الأداء</li>
            </ul>
        </section>

        <!-- المميزات -->
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>أداء عالي</h3>
                    <p>شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم</p>
                </div>
                <div class="feature-card">
                    <h3>موثوقية تامة</h3>
                    <p>حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال</p>
                </div>
                <div class="feature-card">
                    <h3>أمان متقدم</h3>
                    <p>تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح</p>
                </div>
                <div class="feature-card">
                    <h3>قابلية التوسع</h3>
                    <p>شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية</p>
                </div>
                <div class="feature-card">
                    <h3>دعم فني متميز</h3>
                    <p>فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات</p>
                </div>
                <div class="feature-card">
                    <h3>تكلفة مثلى</h3>
                    <p>حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لشبكة موثوقة وعالية الأداء؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', createParticles);

        // نظام الترجمة
        document.getElementById('lang-toggle').addEventListener('click', function() {
            const btn = this;
            const isArabic = document.documentElement.lang === 'ar';

            document.documentElement.lang = isArabic ? 'en' : 'ar';
            btn.textContent = isArabic ? 'AR' : 'EN';

            const translations = {
                'الشبكات': 'Networking',
                'نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع أنظمتكم. من التصميم والتنفيذ إلى الصيانة والدعم، نحن شريككم الموثوق في عالم الشبكات.': 'We provide integrated and reliable network solutions that ensure smooth and secure connectivity for all your systems. From design and implementation to maintenance and support, we are your trusted partner in the networking world.',
                'خدماتنا في الشبكات': 'Our Networking Services',
                'تصميم الشبكات: تصميم شبكات مخصصة تتناسب مع احتياجاتكم وحجم أعمالكم': 'Network Design: Custom network design that suits your needs and business size',
                'تنفيذ وتركيب: تنفيذ احترافي للشبكات باستخدام أحدث المعدات والتقنيات': 'Implementation and Installation: Professional network implementation using the latest equipment and technologies',
                'الشبكات اللاسلكية: حلول Wi-Fi متقدمة وآمنة لجميع البيئات': 'Wireless Networks: Advanced and secure Wi-Fi solutions for all environments',
                'شبكات الخوادم: إعداد وإدارة شبكات الخوادم عالية الأداء': 'Server Networks: Setup and management of high-performance server networks',
                'الصيانة الدورية: خدمات صيانة شاملة لضمان استمرارية الشبكة': 'Periodic Maintenance: Comprehensive maintenance services to ensure network continuity',
                'مراقبة الشبكة: مراقبة مستمرة للأداء واكتشاف المشاكل مبكراً': 'Network Monitoring: Continuous performance monitoring and early problem detection',
                'ترقية الشبكات: تحديث وترقية الشبكات الموجودة لتحسين الأداء': 'Network Upgrades: Updating and upgrading existing networks to improve performance',
                'لماذا تختار خدماتنا؟': 'Why Choose Our Services?',
                'أداء عالي': 'High Performance',
                'شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم': 'High-speed and efficient networks ensuring optimal performance for all your applications and systems',
                'موثوقية تامة': 'Complete Reliability',
                'حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال': 'Reliable network solutions with quality guarantees and backup plans for protection against failures',
                'أمان متقدم': 'Advanced Security',
                'تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح': 'Implementation of the highest security standards to protect your network from threats and unauthorized access',
                'قابلية التوسع': 'Scalability',
                'شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية': 'Flexible networks that can be expanded and developed with your business growth and future requirements',
                'دعم فني متميز': 'Excellent Technical Support',
                'فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات': 'Specialized technical support team available 24/7 to solve any problems or inquiries',
                'تكلفة مثلى': 'Optimal Cost',
                'حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم': 'Cost-effective solutions that achieve the best return on investment for your projects',
                'هل تحتاج لشبكة موثوقة وعالية الأداء؟': 'Do You Need a Reliable and High-Performance Network?',
                'تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات': 'Contact us today for a free consultation and comprehensive assessment of your networking needs',
                'تواصل معنا': 'Contact Us',
                'اتصل بنا': 'Call Us',
                'العودة للرئيسية': 'Back to Home'
            };

            document.querySelectorAll('h1, h2, h3, h4, p, button, a, li').forEach(el => {
                const original = el.textContent.trim();

                if (el.id === 'lang-toggle' || original === 'MCT') {
                    return;
                }

                if (isArabic) {
                    if (translations[original]) {
                        el.textContent = translations[original];
                    }
                } else {
                    for (const [ar, en] of Object.entries(translations)) {
                        if (en === original) {
                            el.textContent = ar;
                            break;
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
