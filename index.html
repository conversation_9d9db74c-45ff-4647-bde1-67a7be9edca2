<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCT - حلول تقنية متطورة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #28909A;
            --secondary-color: #1c5c62;
            --accent-color-1: #F7A600;
            --accent-color-2: #008B80;
            --text-color: #ffffff;
            --bg-dark: #0f0f23;
            --card-bg: rgba(255, 255, 255, 0.05);
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: var(--text-color);
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
        }

        /* نقاط متحركة */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgb(0, 151, 139);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Navbar */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 20px 5%;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: var(--transition);
        }

        .nav-container {
            display: flex;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-right: auto;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3); }
            100% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.6); }
        }

        .company-name {
            font-size: 26px;
            font-weight: bold;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            list-style: none;
            margin: 0 auto;
        }

        .nav-links a {
            position: relative;
            display: inline-block;
            overflow: hidden;
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            transition: var(--transition);
        }

        .nav-links a:hover {
            color: #ffffff;
            transform: scale(1.05);
        }

        .nav-links a::after {
            content: "";
            position: absolute;
            bottom: 8px;
            right: 20px;
            width: 0%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.4s ease;
        }

        .nav-links a:hover::after {
            width: 60%;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 100px;
            padding: 0 5%;
        }

        .section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 50px;
            text-align: center;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            padding-bottom: 20px;
        }

        .section-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(to right, var(--accent-color-2), var(--secondary-color));
            border-radius: 2px;
        }

        /* الصفحة الرئيسية */
        .hero-section {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }

        .hero-logo {
            width: 200px;
            height: 200px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: heroFloat 3s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShine 3s ease-in-out infinite;
        }

        @keyframes textShine {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .hero-content p {
            font-size: 1.3rem;
            line-height: 1.8;
            max-width: 800px;
            color: #b0b0b0;
            margin-bottom: 30px;
        }

        .cta-button {
            padding: 15px 40px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 10px 30px rgba(40, 144, 154, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color-1));
            transition: var(--transition);
            z-index: -1;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(40, 144, 154, 0.5);
        }

        .cta-button:hover::before {
            left: 0;
        }

        /* قسم الخدمات */
        .services-section {
            flex-direction: column;
            text-align: center;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(310px, 1fr));
            gap: 40px;
            width: 100%;
        }

        .service-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(40, 144, 154, 0.1), rgba(118, 75, 162, 0.1));
            transition: left 0.5s ease;
            z-index: -1;
        }

        .service-card:hover::before {
            left: 0;
        }

        .service-card:hover {
            transform: translateY(-10px);
            border-color: rgba(40, 144, 154, 0.3);
            box-shadow: 0 20px 40px rgba(40, 144, 154, 0.2);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 30px;
            color: white;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--accent-color-2);
        }

        .service-card p {
            color: #b0b0b0;
            line-height: 1.6;
        }

        /* قسم فريق العمل */
        .team-section {
            flex-direction: column;
            text-align: center;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .team-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: var(--transition);
        }

        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .team-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            border: 3px solid var(--accent-color-2);
            object-fit: cover;
        }

        .team-card h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .team-card .position {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 15px;
            display: block;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .social-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            transition: var(--transition);
        }

        .social-icon:hover {
            background: var(--accent-color-2);
            transform: translateY(-3px);
        }

        /* قسم المشاريع */
        .projects-section {
            flex-direction: column;
            text-align: center;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .project-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .project-img {
            width: 100%;
            height: 220px;
            object-fit: cover;
            display: block;
        }

        .project-content {
            padding: 25px;
            text-align: right;
        }

        .project-card h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .project-card .category {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 15px;
            display: block;
        }

        .project-card p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* قسم المدونة */
        .blog-section {
            flex-direction: column;
            text-align: center;
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .blog-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            transition: var(--transition);
        }

        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .blog-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
        }

        .blog-content {
            padding: 25px;
            text-align: right;
        }

        .blog-date {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 10px;
            display: block;
        }

        .blog-card h3 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .blog-card p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* قسم العملاء */
        .clients-section {
            flex-direction: column;
            text-align: center;
        }

        .clients-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            width: 100%;
            align-items: center;
        }

        .client-logo {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: var(--transition);
        }

        .client-logo:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 30px rgba(40, 144, 154, 0.2);
        }

        .client-logo img {
            max-width: 100%;
            max-height: 60px;
            filter: grayscale(100%);
            transition: var(--transition);
        }

        .client-logo:hover img {
            filter: grayscale(0%);
        }

        /* قسم التواصل */
        .contact-section {
            flex-direction: column;
            text-align: center;
        }

        .contact-container {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            padding: 60px 40px;
            max-width: 900px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .contact-item {
            text-align: center;
        }

        .contact-item h4 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .contact-item p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .contact-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-item a:hover {
            color: var(--accent-color-2);
            text-decoration: underline;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .social-link {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 20px;
            transition: var(--transition);
        }

        .social-link:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 10px 25px rgba(40, 144, 154, 0.5);
        }

        /* تأثيرات الظهور التدريجي */
        .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: var(--transition);
        }

        .fade-in-element.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-element.delay-1 {
            transition-delay: 0.2s;
        }

        .fade-in-element.delay-2 {
            transition-delay: 0.4s;
        }

        .fade-in-element.delay-3 {
            transition-delay: 0.6s;
        }

        .fade-in-element.delay-4 {
            transition-delay: 0.8s;
        }

        .fade-in-element.delay-5 {
            transition-delay: 1s;
        }

        .fade-in-element.delay-6 {
            transition-delay: 1.2s;
        }

        /* تأثيرات الشهب */
        .meteor {
            position: absolute;
            width: 3px;
            height: 3px;
            background: white;
            box-shadow: 0 0 16px 6px white;
            transform: rotate(35deg);
            animation: shoot 6s linear infinite;
            opacity: 0;
            z-index: -1;
        }

        @keyframes shoot {
            0% {
                top: +0%;
                left: +0%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                top: 60%;
                left: 70%;
                opacity: 0.6;
            }
            100% {
                top: 110%;
                left: 110%;
                opacity: 0;
            }
        }

        /* تذييل الصفحة */
        .footer {
            background: rgba(15, 15, 35, 0.95);
            padding: 40px 5%;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .footer-logo {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            font-weight: bold;
            color: white;
            box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
        }

        .footer-links {
            display: flex;
            gap: 25px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .footer-links a {
            color: #b0b0b0;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--accent-color-2);
        }

        .copyright {
            color: #777;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* زر الانتقال للأعلى */
        .scroll-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--accent-color-2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
            z-index: 999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .scroll-top.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .scroll-top:hover {
            background: var(--accent-color-1);
            transform: translateY(-5px);
        }

        /* وسائط متجاوبة */
        @media (max-width: 992px) {
            .nav-links {
                gap: 15px;
            }
            
            .nav-links a {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
            
            .hero-content h1 {
                font-size: 2.8rem;
            }
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 15px;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .hero-content p {
                font-size: 1.1rem;
            }
            
            .services-grid,
            .projects-grid,
            .blog-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
            
            .section {
                min-height: auto;
                padding: 80px 0;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .contact-container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    
    <div class="floating-particles" id="particles"></div>
    <div class="meteor" id="meteor-1"></div>
    <div class="meteor" id="meteor-2"></div>
    <div class="meteor" id="meteor-3"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="logo">MCT</div>
                <div class="company-name">MCT</div>
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#services">الخدمات</a></li>
                <li><a href="#team">فريقنا</a></li>
                <li><a href="#projects">مشاريعنا</a></li>
                <li><a href="#blog">المدونة</a></li>
                <li><a href="#clients">عملائنا</a></li>
                <li><a href="#contact">تواصل معنا</a></li>
                <li>
                    <button id="lang-toggle" class="cta-button" style="padding: 10px 20px; font-size: 0.9rem;">
                        EN
                    </button>
                </li>
            </ul>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <div class="logo" style="width: 200px; height: 200px; font-size: 80px;">MCT</div>
                </div>
                <div class="hero-content">
                    <h1 class="fade-in-element delay-1">MCT - الحلول التقنية المتكاملة</h1>
                    <p class="fade-in-element delay-2">
                        نقدم حلولاً تقنية متطورة تلبي احتياجات العصر الرقمي. فريقنا من الخبراء المتخصصين يعمل على تقديم خدمات الشبكات، البرمجيات، وأمن المعلومات بأعلى معايير الجودة والاحترافية.
                    </p>
                    <button class="cta-button fade-in-element delay-3">اكتشف خدماتنا</button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-section">
                <h2 class="section-title fade-in-element">خدماتنا المتميزة</h2>
                <div class="services-grid">
                    <div class="service-card fade-in-element delay-1">
                        <div class="service-icon"><i class="fas fa-network-wired"></i></div>
                        <h3>حلول الشبكات</h3>
                        <p>تصميم وتنفيذ وصيانة الشبكات المحلية والواسعة بأحدث التقنيات وأعلى معايير الأمان والأداء.</p>
                    </div>
                    <div class="service-card fade-in-element delay-2">
                        <div class="service-icon"><i class="fas fa-code"></i></div>
                        <h3>تطوير البرمجيات</h3>
                        <p>تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.</p>
                    </div>
                    <div class="service-card fade-in-element delay-3">
                        <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
                        <h3>أمن المعلومات</h3>
                        <p>حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي.</p>
                    </div>
                    <div class="service-card fade-in-element delay-4">
                        <div class="service-icon"><i class="fas fa-search"></i></div>
                        <h3>الفحص الأمني</h3>
                        <p>تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة.</p>
                    </div>
                    <div class="service-card fade-in-element delay-5">
                        <div class="service-icon"><i class="fas fa-tools"></i></div>
                        <h3>الدعم والصيانة</h3>
                        <p>خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل.</p>
                    </div>
                    <div class="service-card fade-in-element delay-6">
                        <div class="service-icon"><i class="fas fa-cloud"></i></div>
                        <h3>الحلول السحابية</h3>
                        <p>توفير حلول سحابية متكاملة تساعد في تخفيض التكاليف وزيادة المرونة والكفاءة.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم فريق العمل -->
        <section id="team" class="section">
            <div class="team-section">
                <h2 class="section-title fade-in-element">فريق العمل الخبير</h2>
                <div class="team-grid">
                    <div class="team-card fade-in-element delay-1">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="مدير تقني" class="team-img">
                        <h3>أحمد محمد</h3>
                        <span class="position">المدير التقني</span>
                        <p>خبرة أكثر من 15 عامًا في إدارة المشاريع التقنية وتطوير الحلول المتكاملة.</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                    <div class="team-card fade-in-element delay-2">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="مديرة أمن المعلومات" class="team-img">
                        <h3>سارة عبدالله</h3>
                        <span class="position">مديرة أمن المعلومات</span>
                        <p>خبيرة في مجال الأمن السيبراني وحماية البيانات مع شهادات متقدمة في المجال.</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                    <div class="team-card fade-in-element delay-3">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="رئيس مطورين" class="team-img">
                        <h3>خالد حسن</h3>
                        <span class="position">رئيس مطورين</span>
                        <p>مطور برمجيات متميز مع خبرة واسعة في تطوير تطبيقات الويب والجوال.</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                    <div class="team-card fade-in-element delay-4">
                        <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="مديرة مشاريع" class="team-img">
                        <h3>لمى علي</h3>
                        <span class="position">مديرة مشاريع</span>
                        <p>مديرة مشاريع معتمدة مع خبرة في إدارة فرق العمل وتحقيق الأهداف ضمن الميزانية والوقت.</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم المشاريع -->
        <section id="projects" class="section">
            <div class="projects-section">
                <h2 class="section-title fade-in-element">مشاريعنا الناجحة</h2>
                <div class="projects-grid">
                    <div class="project-card fade-in-element delay-1">
                        <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="نظام إدارة المؤسسات" class="project-img">
                        <div class="project-content">
                            <span class="category">تطوير برمجيات</span>
                            <h3>نظام إدارة المؤسسات</h3>
                            <p>نظام متكامل لإدارة العمليات المؤسسية يشمل إدارة الموارد البشرية، المالية، والمخازن.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">عرض المشروع</a>
                        </div>
                    </div>
                    <div class="project-card fade-in-element delay-2">
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1115&q=80" alt="منصة تجارة إلكترونية" class="project-img">
                        <div class="project-content">
                            <span class="category">حلول سحابية</span>
                            <h3>منصة تجارة إلكترونية</h3>
                            <p>منصة متكاملة للتجارة الإلكترونية تدعم آلاف المنتجات والمستخدمين المتزامنين.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">عرض المشروع</a>
                        </div>
                    </div>
                    <div class="project-card fade-in-element delay-3">
                        <img src="https://images.unsplash.com/photo-1558346490-a72e53ae2d4f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="تأمين شبكة المؤسسات" class="project-img">
                        <div class="project-content">
                            <span class="category">أمن المعلومات</span>
                            <h3>تأمين شبكة المؤسسات</h3>
                            <p>تأمين شبكة مؤسسة حكومية كبيرة ضد الهجمات الإلكترونية وحماية البيانات الحساسة.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">عرض المشروع</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم المدونة -->
        <section id="blog" class="section">
            <div class="blog-section">
                <h2 class="section-title fade-in-element">أحدث المقالات</h2>
                <div class="blog-grid">
                    <div class="blog-card fade-in-element delay-1">
                        <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="أمن المعلومات" class="blog-img">
                        <div class="blog-content">
                            <span class="blog-date">15 يونيو 2023</span>
                            <h3>أهمية أمن المعلومات في المؤسسات الحديثة</h3>
                            <p>كيف تحمي مؤسستك من الهجمات الإلكترونية المتطورة وتضمن استمرارية العمل في العصر الرقمي.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">قراءة المزيد</a>
                        </div>
                    </div>
                    <div class="blog-card fade-in-element delay-2">
                        <img src="https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="التقنيات الناشئة" class="blog-img">
                        <div class="blog-content">
                            <span class="blog-date">5 مايو 2023</span>
                            <h3>أهم التقنيات الناشئة في 2023 وكيف تستفيد منها</h3>
                            <p>استكشاف أحدث التقنيات مثل الذكاء الاصطناعي، البلوك تشين، والحوسبة الكمية وتطبيقاتها العملية.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">قراءة المزيد</a>
                        </div>
                    </div>
                    <div class="blog-card fade-in-element delay-3">
                        <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="الحوسبة السحابية" class="blog-img">
                        <div class="blog-content">
                            <span class="blog-date">20 أبريل 2023</span>
                            <h3>دليل شامل للهجرة إلى الحوسبة السحابية</h3>
                            <p>كيف تنتقل مؤسستك إلى السحابة بسلاسة وتستفيد من مزاياها في توفير التكاليز وزيادة المرونة.</p>
                            <a href="#" class="cta-button" style="padding: 8px 20px; font-size: 0.9rem;">قراءة المزيد</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم العملاء -->
        <section id="clients" class="section">
            <div class="clients-section">
                <h2 class="section-title fade-in-element">عملاؤنا الكرام</h2>
                <div class="clients-grid">
                    <div class="client-logo fade-in-element delay-1">
                        <i class="fas fa-building" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                    <div class="client-logo fade-in-element delay-2">
                        <i class="fas fa-university" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                    <div class="client-logo fade-in-element delay-3">
                        <i class="fas fa-hospital" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                    <div class="client-logo fade-in-element delay-4">
                        <i class="fas fa-plane" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                    <div class="client-logo fade-in-element delay-5">
                        <i class="fas fa-shopping-cart" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                    <div class="client-logo fade-in-element delay-6">
                        <i class="fas fa-landmark" style="font-size: 40px; color: var(--accent-color-2);"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم التواصل -->
        <section id="contact" class="section">
            <div class="contact-section">
                <h2 class="section-title fade-in-element">تواصل معنا</h2>
                <div class="contact-container fade-in-element delay-1">
                    <div class="contact-grid">
                        <div class="contact-item">
                            <h4><i class="fas fa-envelope"></i> البريد الإلكتروني</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-phone"></i> أرقام الهواتف</h4>
                            <p><a href="tel:+966123456789">+966 12 345 6789</a></p>
                            <p><a href="tel:+966987654321">+966 98 765 4321</a></p>
                            <p><a href="tel:+966555123456">+966 55 512 3456</a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-map-marker-alt"></i> العنوان</h4>
                            <p>الرياض، المملكة العربية السعودية</p>
                            <p>شارع الملك فهد، برج التقنية</p>
                            <p>الطابق 10، مكتب 1002</p>
                        </div>
                    </div>
                    
                    <h4 style="color: var(--accent-color-2); margin: 30px 0 20px;">🌐 تابعنا على وسائل التواصل الاجتماعي</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link" title="تويتر"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link" title="انستغرام"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link" title="يوتيوب"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-link" title="واتساب"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- تذييل الصفحة -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">MCT</div>
            <p>نقدم حلولاً تقنية مبتكرة تلبي احتياجات العصر الرقمي</p>
            <div class="footer-links">
                <a href="#home">الرئيسية</a>
                <a href="#services">الخدمات</a>
                <a href="#team">فريق العمل</a>
                <a href="#projects">المشاريع</a>
                <a href="#blog">المدونة</a>
                <a href="#clients">العملاء</a>
                <a href="#contact">تواصل معنا</a>
            </div>
            <p class="copyright">© 2023 MCT جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- زر الانتقال للأعلى -->
    <div class="scroll-top" id="scrollTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثير التمرير السلس
        function smoothScroll() {
            const navLinks = document.querySelectorAll('.nav-links a, .cta-button, .footer-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            targetSection.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }

        // تأثير شريط التنقل عند التمرير
        function navbarScrollEffect() {
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                    navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                } else {
                    navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                    navbar.style.boxShadow = 'none';
                }
            });
        }

        // تأثير الظهور التدريجي عند التمرير
        function scrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
            const fadeElements = document.querySelectorAll('.fade-in-element');
            fadeElements.forEach(element => {
                observer.observe(element);
            });
        }

        // زر الانتقال للأعلى
        function setupScrollTop() {
            const scrollTopBtn = document.getElementById('scrollTop');
            
            window.addEventListener('scroll', () => {
                if (window.scrollY > 500) {
                    scrollTopBtn.classList.add('visible');
                } else {
                    scrollTopBtn.classList.remove('visible');
                }
            });
            
            scrollTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // تكرار حركة الشهاب
        function randomizeMeteor() {
            const meteors = document.querySelectorAll('.meteor');
            
            meteors.forEach(meteor => {
                meteor.style.top = Math.random() * 10 + '%';
                meteor.style.left = Math.random() * 10 + '%';
                meteor.style.animationDelay = Math.random() * 5 + 's';
            });
        }

        // تهيئة الموقع
        function initWebsite() {
            createParticles();
            smoothScroll();
            navbarScrollEffect();
            scrollAnimations();
            setupScrollTop();
            randomizeMeteor();
            setInterval(randomizeMeteor, 5000);
        }

        // تشغيل التهيئة عند تحميل الصفحة
        window.addEventListener('load', initWebsite);
    </script>
</body>
</html>