<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCT - حلول تقنية متطورة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #28909A;
            --secondary-color: #1c5c62;
            --accent-color-1: #F7A600;
            --accent-color-2: #008B80;
            --text-color: #ffffff;
            --bg-dark: #0f0f23;
            --card-bg: rgba(255, 255, 255, 0.05);
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: var(--text-color);
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
        }

        /* نقاط متحركة */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgb(0, 151, 139);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Navbar */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 20px 5%;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: var(--transition);
        }

        .nav-container {
            display: flex;
            align-items: center;
            width: 100%;
            margin: 0 auto;
            position: relative;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-right: auto;
        }

        .logo {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
            animation: logoGlow 3s ease-in-out infinite alternate;
            overflow: hidden;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3); }
            100% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.6); }
        }

        .company-name {
            font-size: 26px;
            font-weight: bold;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            list-style: none;
            margin: 0 auto;
        }

        .nav-links a {
            position: relative;
            display: inline-block;
            overflow: hidden;
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            transition: var(--transition);
        }

        .nav-links a:hover {
            color: #ffffff;
            transform: scale(1.05);
        }

        .nav-links a::after {
            content: "";
            position: absolute;
            bottom: 8px;
            right: 20px;
            width: 0%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.4s ease;
        }

        .nav-links a:hover::after {
            width: 60%;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 100px;
            padding: 0 2%;
            width: 100%;
        }

        .section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
            width: 100%;
            margin: 0 auto;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 50px;
            text-align: center;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            padding-bottom: 20px;
        }

        .section-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(to right, var(--accent-color-2), var(--secondary-color));
            border-radius: 2px;
        }

        /* الصفحة الرئيسية */
        .hero-section {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }

        .hero-logo {
            width: 200px;
            height: 200px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: heroFloat 3s ease-in-out infinite;
            overflow: hidden;
        }

        .hero-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShine 3s ease-in-out infinite;
        }

        @keyframes textShine {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .hero-content p {
            font-size: 1.3rem;
            line-height: 1.8;
            max-width: 800px;
            color: #b0b0b0;
            margin-bottom: 30px;
        }

        .cta-button {
            padding: 15px 40px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 10px 30px rgba(40, 144, 154, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color-1));
            transition: var(--transition);
            z-index: -1;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(40, 144, 154, 0.5);
        }

        .cta-button:hover::before {
            left: 0;
        }

        /* قسم الخدمات */
        .services-section {
            flex-direction: column;
            text-align: center;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
        }

        .service-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(40, 144, 154, 0.1), rgba(118, 75, 162, 0.1));
            transition: left 0.5s ease;
            z-index: -1;
        }

        .service-card:hover::before {
            left: 0;
        }

        .service-card:hover {
            transform: translateY(-10px);
            border-color: rgba(40, 144, 154, 0.3);
            box-shadow: 0 20px 40px rgba(40, 144, 154, 0.2);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 30px;
            color: white;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--accent-color-2);
        }

        .service-card p {
            color: #b0b0b0;
            line-height: 1.6;
        }

        /* قسم فريق العمل */
        .team-section {
            flex-direction: column;
            text-align: center;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .team-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: var(--transition);
        }

        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .team-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            border: 3px solid var(--accent-color-2);
            object-fit: cover;
        }

        .team-card h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .team-card .position {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 15px;
            display: block;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .social-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            transition: var(--transition);
        }

        .social-icon:hover {
            background: var(--accent-color-2);
            transform: translateY(-3px);
        }

        /* قسم المشاريع */
        .projects-section {
            flex-direction: column;
            text-align: center;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .project-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .project-img {
            width: 100%;
            height: 220px;
            object-fit: cover;
            display: block;
        }

        .project-content {
            padding: 25px;
            text-align: right;
        }

        .project-card h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .project-card .category {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 15px;
            display: block;
        }

        .project-card p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* قسم المدونة */
        .blog-section {
            flex-direction: column;
            text-align: center;
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .blog-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            transition: var(--transition);
        }

        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .blog-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
        }

        .blog-content {
            padding: 25px;
            text-align: right;
        }

        .blog-date {
            color: var(--accent-color-2);
            font-weight: 600;
            margin-bottom: 10px;
            display: block;
        }

        .blog-card h3 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .blog-card p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* قسم العملاء */
        .clients-section {
            flex-direction: column;
            text-align: center;
        }

        .clients-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            width: 100%;
            align-items: center;
        }

        .client-logo {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: var(--transition);
        }

        .client-logo:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 30px rgba(40, 144, 154, 0.2);
        }

        .client-logo img {
            max-width: 100%;
            max-height: 60px;
            filter: grayscale(100%);
            transition: var(--transition);
        }

        .client-logo:hover img {
            filter: grayscale(0%);
        }

        /* قسم التواصل */
        .contact-section {
            flex-direction: column;
            text-align: center;
        }

        .contact-container {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            padding: 60px 40px;
            max-width: 1400px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .contact-item {
            text-align: center;
        }

        .contact-item h4 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .contact-item p {
            color: #b0b0b0;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .contact-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-item a:hover {
            color: var(--accent-color-2);
            text-decoration: underline;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .social-link {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 20px;
            transition: var(--transition);
        }

        .social-link:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 10px 25px rgba(40, 144, 154, 0.5);
        }

        /* تأثيرات الظهور التدريجي */
        .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: var(--transition);
        }

        .fade-in-element.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-element.delay-1 {
            transition-delay: 0.2s;
        }

        .fade-in-element.delay-2 {
            transition-delay: 0.4s;
        }

        .fade-in-element.delay-3 {
            transition-delay: 0.6s;
        }

        .fade-in-element.delay-4 {
            transition-delay: 0.8s;
        }

        .fade-in-element.delay-5 {
            transition-delay: 1s;
        }

        .fade-in-element.delay-6 {
            transition-delay: 1.2s;
        }

        /* تأثيرات الشهب */
        .meteor {
            position: absolute;
            width: 3px;
            height: 3px;
            background: white;
            box-shadow: 0 0 16px 6px white;
            transform: rotate(35deg);
            animation: shoot 6s linear infinite;
            opacity: 0;
            z-index: -1;
        }

        @keyframes shoot {
            0% {
                top: +0%;
                left: +0%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                top: 60%;
                left: 70%;
                opacity: 0.6;
            }
            100% {
                top: 110%;
                left: 110%;
                opacity: 0;
            }
        }

        /* تذييل الصفحة */
        .footer {
            background: rgba(15, 15, 35, 0.95);
            padding: 40px 5%;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .footer-logo {
            width: 70px;
            height: 70px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
            overflow: hidden;
        }

        .footer-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .footer-links {
            display: flex;
            gap: 25px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .footer-links a {
            color: #b0b0b0;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--accent-color-2);
        }

        .copyright {
            color: #777;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* زر الانتقال للأعلى */
        .scroll-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--accent-color-2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
            z-index: 999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .scroll-top.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .scroll-top:hover {
            background: var(--accent-color-1);
            transform: translateY(-5px);
        }

        /* وسائط متجاوبة */
        @media (max-width: 992px) {
            .nav-links {
                gap: 15px;
            }
            
            .nav-links a {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
            
            .hero-content h1 {
                font-size: 2.8rem;
            }
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 15px;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .hero-content p {
                font-size: 1.1rem;
            }
            
            .services-grid,
            .projects-grid,
            .blog-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
            
            .section {
                min-height: auto;
                padding: 80px 0;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .contact-container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    
    <div class="floating-particles" id="particles"></div>
    <div class="meteor" id="meteor-1"></div>
    <div class="meteor" id="meteor-2"></div>
    <div class="meteor" id="meteor-3"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="company-name">MCT</div>
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#services">الخدمات</a></li>
                <li><a href="#contact">تواصل معنا</a></li>
                <li>
                    <button id="lang-toggle" class="cta-button" style="padding: 10px 20px; font-size: 0.9rem;">
                        EN
                    </button>
                </li>
            </ul>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="hero-content">
                    <h1 class="fade-in-element delay-1">MCT - الحلول التقنية المتكاملة</h1>
                    <p class="fade-in-element delay-2">
                        نقدم حلولاً تقنية متطورة تلبي احتياجات العصر الرقمي. فريقنا من الخبراء المتخصصين يعمل على تقديم خدمات الشبكات، البرمجيات، وأمن المعلومات بأعلى معايير الجودة والاحترافية.
                    </p>
                    <button class="cta-button fade-in-element delay-3">اكتشف خدماتنا</button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-section">
                <h2 class="section-title fade-in-element">خدماتنا المتميزة</h2>
                <div class="services-grid">
                    <a href="networking.html" class="service-card fade-in-element delay-1" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-network-wired"></i></div>
                        <h3>حلول الشبكات</h3>
                        <p>تصميم وتنفيذ وصيانة الشبكات المحلية والواسعة بأحدث التقنيات وأعلى معايير الأمان والأداء.</p>
                    </a>
                    <a href="software.html" class="service-card fade-in-element delay-2" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-code"></i></div>
                        <h3>تطوير البرمجيات</h3>
                        <p>تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.</p>
                    </a>
                    <a href="cybersecurity.html" class="service-card fade-in-element delay-3" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
                        <h3>أمن المعلومات</h3>
                        <p>حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي.</p>
                    </a>
                    <a href="security-assessment.html" class="service-card fade-in-element delay-4" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-search"></i></div>
                        <h3>الفحص الأمني</h3>
                        <p>تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة.</p>
                    </a>
                    <a href="maintenance.html" class="service-card fade-in-element delay-5" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-tools"></i></div>
                        <h3>الدعم والصيانة</h3>
                        <p>خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل.</p>
                    </a>
                    <a href="consulting.html" class="service-card fade-in-element delay-6" style="text-decoration: none; color: inherit;">
                        <div class="service-icon"><i class="fas fa-cloud"></i></div>
                        <h3>الحلول السحابية</h3>
                        <p>توفير حلول سحابية متكاملة تساعد في تخفيض التكاليف وزيادة المرونة والكفاءة.</p>
                    </a>
                </div>
            </div>
        </section>









        <!-- قسم التواصل -->
        <section id="contact" class="section">
            <div class="contact-section">
                <h2 class="section-title fade-in-element">تواصل معنا</h2>
                <div class="contact-container fade-in-element delay-1">
                    <div class="contact-grid">
                        <div class="contact-item">
                            <h4><i class="fas fa-envelope"></i> البريد الإلكتروني</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-phone"></i> أرقام الهواتف</h4>
                            <p><a href="tel:+966123456789">+966 12 345 6789</a></p>
                            <p><a href="tel:+966987654321">+966 98 765 4321</a></p>
                            <p><a href="tel:+966555123456">+966 55 512 3456</a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-map-marker-alt"></i> العنوان</h4>
                            <p>الرياض، المملكة العربية السعودية</p>
                            <p>شارع الملك فهد، برج التقنية</p>
                            <p>الطابق 10، مكتب 1002</p>
                        </div>
                    </div>
                    
                    <h4 style="color: var(--accent-color-2); margin: 30px 0 20px;">🌐 تابعنا على وسائل التواصل الاجتماعي</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link" title="تويتر"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link" title="انستغرام"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link" title="يوتيوب"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-link" title="واتساب"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- تذييل الصفحة -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="image/logo-8.png" alt="MCT Logo">
            </div>
            <p>نقدم حلولاً تقنية مبتكرة تلبي احتياجات العصر الرقمي</p>
            <div class="footer-links">
                <a href="#home">الرئيسية</a>
                <a href="#services">الخدمات</a>
                <a href="#contact">تواصل معنا</a>
            </div>
            <p class="copyright">© 2023 MCT جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- زر الانتقال للأعلى -->
    <div class="scroll-top" id="scrollTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثير التمرير السلس
        function smoothScroll() {
            const navLinks = document.querySelectorAll('.nav-links a, .cta-button, .footer-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            targetSection.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }

        // تأثير شريط التنقل عند التمرير
        function navbarScrollEffect() {
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                    navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                } else {
                    navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                    navbar.style.boxShadow = 'none';
                }
            });
        }

        // تأثير الظهور التدريجي عند التمرير
        function scrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
            const fadeElements = document.querySelectorAll('.fade-in-element');
            fadeElements.forEach(element => {
                observer.observe(element);
            });
        }

        // زر الانتقال للأعلى
        function setupScrollTop() {
            const scrollTopBtn = document.getElementById('scrollTop');
            
            window.addEventListener('scroll', () => {
                if (window.scrollY > 500) {
                    scrollTopBtn.classList.add('visible');
                } else {
                    scrollTopBtn.classList.remove('visible');
                }
            });
            
            scrollTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // تكرار حركة الشهاب
        function randomizeMeteor() {
            const meteors = document.querySelectorAll('.meteor');
            
            meteors.forEach(meteor => {
                meteor.style.top = Math.random() * 10 + '%';
                meteor.style.left = Math.random() * 10 + '%';
                meteor.style.animationDelay = Math.random() * 5 + 's';
            });
        }

        // تهيئة الموقع
        function initWebsite() {
            createParticles();
            smoothScroll();
            navbarScrollEffect();
            scrollAnimations();
            setupScrollTop();
            randomizeMeteor();
            setInterval(randomizeMeteor, 5000);
        }

        // نظام الترجمة المحسن
        function setupTranslation() {
            const langToggle = document.getElementById('lang-toggle');

            const translations = {
                'ar': {
                    'الرئيسية': 'Home',
                    'الخدمات': 'Services',
                    'تواصل معنا': 'Contact Us',
                    'MCT - الحلول التقنية المتكاملة': 'MCT - Integrated Technology Solutions',
                    'نقدم حلولاً تقنية متطورة تلبي احتياجات العصر الرقمي. فريقنا من الخبراء المتخصصين يعمل على تقديم خدمات الشبكات، البرمجيات، وأمن المعلومات بأعلى معايير الجودة والاحترافية.': 'We provide advanced technology solutions that meet the needs of the digital age. Our team of specialized experts works to provide networking, software, and information security services with the highest standards of quality and professionalism.',
                    'اكتشف خدماتنا': 'Discover Our Services',
                    'خدماتنا المتميزة': 'Our Distinguished Services',
                    'حلول الشبكات': 'Networking Solutions',
                    'تصميم وتنفيذ وصيانة الشبكات المحلية والواسعة بأحدث التقنيات وأعلى معايير الأمان والأداء.': 'Design, implementation and maintenance of local and wide area networks with the latest technologies and highest standards of security and performance.',
                    'تطوير البرمجيات': 'Software Development',
                    'تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.': 'Development of custom applications and software using the latest technologies to meet different business needs.',
                    'أمن المعلومات': 'Information Security',
                    'حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي.': 'Protecting data and systems from electronic threats and implementing best digital security practices.',
                    'الفحص الأمني': 'Security Assessment',
                    'تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة.': 'Evaluation and examination of systems and networks to discover security vulnerabilities and provide necessary recommendations.',
                    'الدعم والصيانة': 'Support and Maintenance',
                    'خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل.': 'Periodic and emergency maintenance services for technical systems and equipment to ensure business continuity.',
                    'الحلول السحابية': 'Cloud Solutions',
                    'توفير حلول سحابية متكاملة تساعد في تخفيض التكاليف وزيادة المرونة والكفاءة.': 'Providing integrated cloud solutions that help reduce costs and increase flexibility and efficiency.',
                    'البريد الإلكتروني': 'Email',
                    'أرقام الهواتف': 'Phone Numbers',
                    'العنوان': 'Address',
                    'الرياض، المملكة العربية السعودية': 'Riyadh, Saudi Arabia',
                    'شارع الملك فهد، برج التقنية': 'King Fahd Street, Technology Tower',
                    'الطابق 10، مكتب 1002': 'Floor 10, Office 1002',
                    '🌐 تابعنا على وسائل التواصل الاجتماعي': '🌐 Follow us on social media',
                    'نقدم حلولاً تقنية مبتكرة تلبي احتياجات العصر الرقمي': 'We provide innovative technology solutions that meet the needs of the digital age',
                    '© 2023 MCT جميع الحقوق محفوظة': '© 2023 MCT All Rights Reserved'
                }
            };

            langToggle.addEventListener('click', function() {
                const isArabic = document.documentElement.lang === 'ar';
                const newLang = isArabic ? 'en' : 'ar';
                const newDir = isArabic ? 'ltr' : 'rtl';

                document.documentElement.lang = newLang;
                document.documentElement.dir = newDir;
                this.textContent = isArabic ? 'AR' : 'EN';

                if (isArabic && translations.ar) {
                    // Translate from Arabic to English
                    document.querySelectorAll('h1, h2, h3, h4, p, button, a').forEach(el => {
                        const text = el.textContent.trim();
                        if (translations.ar[text] && el.id !== 'lang-toggle' && text !== 'MCT') {
                            el.textContent = translations.ar[text];
                        }
                    });
                } else {
                    // Translate from English to Arabic
                    document.querySelectorAll('h1, h2, h3, h4, p, button, a').forEach(el => {
                        const text = el.textContent.trim();
                        for (const [ar, en] of Object.entries(translations.ar)) {
                            if (en === text && el.id !== 'lang-toggle' && text !== 'MCT') {
                                el.textContent = ar;
                                break;
                            }
                        }
                    });
                }
            });
        }

        // تشغيل التهيئة عند تحميل الصفحة
        window.addEventListener('load', () => {
            initWebsite();
            setupTranslation();
        });
    </script>
</body>
</html>